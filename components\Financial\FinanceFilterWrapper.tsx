"use client";
// import { getFavorites } from '@/actions/favorites.action';
import {
    BadgeCheck,
  ChevronDown,
  FilterIcon,
  Loader2,
  PlusCircle,
  SlidersHorizontal,
  Store,
  Wallet,
} from "lucide-react";
import Image from "next/image";
import React, { useState } from "react";
import MeliBank from "@/public/assets/images/5.png"
import MelatBank from "@/public/assets/images/Melaat_bank.png"
import BankAccountInfoCard from "./BankAccountInfoCard";
import WithdrawRequest from "./WithdrawRequest";
import TransactionsHistory from "./TransactionsHistory";
import ChargeWallet from "./ChargeWallet";
import BankAccountsList from "./BankAccountsList";
// import FavoritesPageList from './FavoritesPageList';
// import { FavoriteItem, FavoritesResponseData } from '@/lib/types/favorites.types';
// import Pagination from '@/components/common/Pagination';

const FinanceFilterWrapper = () => {
  const [activeFilter, setActiveFilter] = useState<
    "transactions_history" | "accounts" | "charge_account" | "withdraw_request"
  >("");
  const [loading, setLoading] = useState(false);
  const [filteredItems, setFilteredItems] = useState([]);
  // pagnation statses
  const [currentPage, setCurrentPage] = useState(1);
  const [lastPage, setLastPage] = useState(2);
  const limit = 10; //favorites.pagination.limit

  const filters: {
    label: string;
    value:
      | "transactions_history"
      | "accounts"
      | "charge_account"
      | "withdraw_request";
  }[] = [
    { label: "تاریخچه تراکنش ها", value: "transactions_history" },
    { label: "حساب ها", value: "accounts" },
    { label: "افزایش موجودی", value: "charge_account" },
    { label: "درخواست برداشت", value: "withdraw_request" },
  ];
  const mobileFilters: {
    label: string;
    value:
      | "transactions_history"
      | "accounts"
      | "charge_account"
      | "withdraw_request";
  }[] = [
    { label: " تراکنش ها", value: "transactions_history" },
    { label: "حساب ها", value: "accounts" },
    { label: " واریز", value: "charge_account" },
    { label: " برداشت", value: "withdraw_request" },
  ];

  const handleFilterClick = async (status: typeof activeFilter) => {
    setActiveFilter(status);
    fetchFavorites(1, status); // returns to first page when filter changes
  };

  const fetchFavorites = async (page: number, sort: string) => {
    // setLoading(true);
    // const res = await getFavorites(page, limit, sort);
    // if (res.success) {
    //     setFavoriteProducts(res.data.products);
    //     setCurrentPage(res.data.pagination.current_page);
    //     setLastPage(res.data.pagination.last_page);
    // }
    // setLoading(false);
  };

  return (
    <div className="">
      <div className="bg-white  flex justify-between items-center w-full border border-gray-200 rounded-b-2xl px-5 border-t-0">
        <div className="flex flex-col md:flex-row gap-3 md:gap-5 items-center h-auto md:h-10 max-md:px-3 py-10 p-3 max-md:w-full">
          <div className="flex items-center gap-2 max-md:hidden">
            <SlidersHorizontal />
            <span>مرتب سازی:</span>
          </div>

          <div className="hidden md:flex md:gap-8">
            {filters.map(({ label, value }) => {
              const isActive = activeFilter === value;
              return (
                <button
                  key={value}
                  onClick={() => handleFilterClick(value)}
                  className={`transition-all ${
                    isActive ? "text-primary border-b-2 border-primary" : ""
                  }`}
                >
                  {label}
                  {/* <span className={`mr-2 rounded-full text-sm px-2 border ${isActive ? 'bg-[#F7BC06] text-black' : 'bg-[#F9FAFB] text-gray-500'}`}>
                                        {counts[countKey]}
                                    </span> */}
                </button>
              );
            })}
          </div>

          {/* mobile version */}
          <div className="md:hidden flex justify-between items-center w-full">
            {mobileFilters.map(({ label, value }) => {
              const isActive = activeFilter === value;
              return (
                <button
                  key={value}
                  onClick={() => handleFilterClick(value)}
                  className={`transition-all font-light ${
                    isActive ? "text-primary border-b-2 border-primary" : ""
                  }`}
                >
                  {label}
                  {/* <span className={`mr-2 rounded-full text-sm px-2 border ${isActive ? 'bg-[#F7BC06] text-black' : 'bg-[#F9FAFB] text-gray-500'}`}>
                                        {counts[countKey]}
                                    </span> */}
                </button>
              );
            })}
          </div>
          {/* <div className="w-full md:hidden">
            <div className="relative w-full">
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <FilterIcon className="w-5 h-5 text-gray-400" />
              </div>

              <select
                value={activeFilter}
                onChange={(e) =>
                  handleFilterClick(e.target.value as typeof activeFilter)
                }
                className="w-full appearance-none border border-gray-300 rounded-xl py-2 pr-10 pl-8 text-sm text-gray-700 bg-white focus:ring-2 focus:ring-gray-200"
              >
                {filters.map(({ label, value }) => (
                  <option key={value} value={value}>
                    {label}
                  </option>
                ))}
              </select>
              <ChevronDown className="w-4 h-4 absolute left-2 top-1/2 transform -translate-y-1/2" />
            </div>
          </div> */}
        </div>
      </div>
      {/* <div className="bg-white mt-8 rounded-2xl border border-gray-200"> */}
        {loading ? (
          <div className="flex flex-1 justify-center items-center min-h-[300px]">
            <Loader2 className="animate-spin w-10 h-10 text-primary" />
          </div>
        ) : (
            // <WithdrawRequest />
              // <TransactionsHistory />
              // <ChargeWallet />

          <BankAccountsList />
        )}
      {/* </div> */}
      {/* <FavoritesPageList favorites={favoriteProducts || []} setFavoriteProducts={setFavoriteProducts} /> */}
    </div>
  );
};

export default FinanceFilterWrapper;
