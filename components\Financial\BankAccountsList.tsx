import { PlusCircle, Wallet } from 'lucide-react'
import React from 'react'
import BankAccountInfoCard from './BankAccountInfoCard'

const BankAccountsList = () => {
    return (
        <div className='bg-white mt-8 rounded-2xl border border-gray-200'>
            {/* // accounts */}
            <div className="p-3 px-5">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3 ">
                        <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3  rounded-b-4xl">
                            <Wallet />
                        </div>
                        <h1 className="md:text-lg text-base"> شماره کارت ها </h1>
                    </div>
                    <div className="flex gap-3">
                        <button className="bg-white border border-gray-200 text-primary flex items-center gap-2 flex-row-reverse font-light px-5 py-3 rounded-xl cursor-pointer text-sm">
                            {" "}
                            افزودن حساب جدید <PlusCircle size={18} />{" "}
                        </button>
                    </div>
                </div>
                <div className="mt-5 space-y-6">

                    <BankAccountInfoCard />
                    <BankAccountInfoCard />
                </div>
            </div>
        </div>
    )
}

export default BankAccountsList