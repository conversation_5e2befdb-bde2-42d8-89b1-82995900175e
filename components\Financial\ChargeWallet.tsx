"use client";
import RoundedArrow from "@/public/assets/images/arrow.webp"
import { Minus, Plus } from "lucide-react";
import Image from "next/image";
import { ChangeEvent, useEffect, useState } from "react";


const predefinedAmounts = [100000, 200000, 500000];
const MIN_AMOUNT = 100_000
const MAX_AMOUNT = 1000_000
const ChargeWallet = () => {
    const [amount, setAmount] = useState(predefinedAmounts[0]);
    const [errorMessage, setErrorMessage] = useState("");
    const [increaseBtnDisabled, setIncreaseBtnDisabled] = useState(false)
    const [decreaseBtnDisabled, setDecreaseBtnDisabled] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const message = "مبلغ باید بین 100000 تا 1000000 تومان باشد"

    const handleAmountChange = (value: number) => {
        if (value < MIN_AMOUNT || value > MAX_AMOUNT) {
            setErrorMessage(message);
        } else {
            setErrorMessage("")
        }
        setAmount(value);
    };

    // TODO: fix this shit. it could be just some inline code without any state o useEffect
    useEffect(() => {
        setDecreaseBtnDisabled(false)
        setIncreaseBtnDisabled(false)
        if (amount <= MIN_AMOUNT) {
            setDecreaseBtnDisabled(true)
        } else if (amount >= MAX_AMOUNT) {
            setIncreaseBtnDisabled(true)
        }
    }, [amount]);
    const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
        const rawValue = e.currentTarget.value.replace(/,/g, ""); // Remove commas
        const numericValue = Number(rawValue);
        if (numericValue < MIN_AMOUNT || numericValue > MAX_AMOUNT) {
            setErrorMessage(message);
        } else {
            setErrorMessage("")
        }
        if (!isNaN(numericValue)) {
            setAmount(numericValue);
        }
    };

    const onPaymentButtonClick = () => {
        setIsLoading(true)
        // TODO: handle payment
        setTimeout(() => {
            setIsLoading(false)
        }, 3000);
    }



    return (
        <div className='md:grid grid-cols-2 gap-4 mt-5 max-md:space-y-5 '>
            <div className='bg-white border border-gray-200 rounded-3xl p-5'>
                {/* Main content with bottom padding for sticky button on mobile */}
                <div className="flex flex-col items-center bg-white rounded-lg w-full mx-auto">

                    <div className="text-right w-full pb-2 ">
                        <h2 className="increase-amount flex text-lg font-semibold text-gray-800 gap-2 mb-3">
                            افزایش موجودی
                            <Image src={RoundedArrow} alt="rounded arrow" />
                        </h2>
                        <p className="text-gray-500 text-sm mt-1">مبلغ مورد نظر خود را برای افزایش اعتبار انتخاب کنید</p>

                    </div>
                    <div className="flex gap-2 w-full my-4">
                        {predefinedAmounts.map((amt) => (
                            <button
                                key={amt}
                                className={`whitespace-nowrap cursor-pointer border-2 border-dashed hover:border-gray-500 md:px-4 py-4 rounded-3xl text-gray-700 w-full font-bold  transition-all ${amount === amt ? "border-green-400 bg-[#F6FFF9]" : "border-gray-300"
                                    }`}
                                onClick={() => handleAmountChange(amt)}
                            >
                                <span className='text-[#596068]'>{amt.toLocaleString()}</span> <span
                                    className='text-xs text-[#596068]'>تومان</span>
                            </button>
                        ))}
                    </div>

                    <div
                        className={`flex items-center border ${errorMessage ? "border-red-500" : "border-gray-300"} rounded-2xl px-5 py-3 w-full`}>
                        <button
                            className="bg-[#C5CBD4] disabled:bg-[#C5CBD4]/50 rounded-full flex items-center justify-center w-10 h-8"
                            onClick={() => handleAmountChange(amount + 10000)}
                            disabled={increaseBtnDisabled}
                        >
                            <Plus size={16} color="white" />
                        </button>
                        <input
                            type="tel"
                            dir="rtl"
                            className="left-direction text-center w-full bg-transparent text-lg font-semibold focus:outline-none"
                            value={amount.toLocaleString()}
                            maxLength={8}
                            onChange={handleInputChange}
                        />

                        <button
                            className=" bg-[#C5CBD4] disabled:bg-[#C5CBD4]/50 rounded-full flex items-center justify-center w-10 h-8"
                            onClick={() => handleAmountChange(amount - 10000)}
                            disabled={decreaseBtnDisabled}
                        >
                            <Minus size={16} color="white" />
                        </button>
                    </div>

                    {errorMessage && <p className="text-red-500 text-sm mt-2">{errorMessage}</p>}

                    {/* Button for desktop - hidden on mobile */}
                    <button
                        className='py-6 mt-4 hidden md:block bg-primary-dark text-white rounded-2xl w-full'
                        // disa={isLoading}
                        disabled={isLoading || !!errorMessage}
                        onClick={onPaymentButtonClick}
                    >
                        افزایش موجودی
                    </button>
                </div>

                {/* Sticky button for mobile - hidden on desktop */}
                <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-5 md:hidden z-50">
                    <button
                        className='bg-primary-dark text-white rounded-2xl w-full py-4'
                        // loading={isLoading}
                        disabled={isLoading || !!errorMessage}
                        onClick={onPaymentButtonClick}
                    >
                        افزایش موجودی
                    </button>
                </div>
            </div>
            <div className='bg-white border border-gray-200 rounded-3xl p-5 max-md:mb-20'>
                <div className="text-right w-full pb-2 ">
                    <h2 className="increase-amount flex text-lg font-semibold text-gray-800 gap-2 mb-3">
                        توضیحات
                        <Image src={RoundedArrow} alt="rounded arrow" />
                    </h2>
                </div>
                <div className="space-y-5">
                    <p>
                        لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد.

                    </p>
                    <p>


                        لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم -
                        در این صورت می توان امید داشت که تمام و دشواری موجود در ارائه راهکارها و شرایط سخت تایپ به پایان رسد وزمان مورد نیاز شامل حروفچینی دستاوردهای اصلی و جوابگوی سوالات پیوسته اهل دنیای موجود طراحی اساسا مورد استفاده قرار گیرد.



                    </p>
                  
                </div>
            </div>
        </div>
    )
}

export default ChargeWallet